"""
规则详情路由API单元测试
测试rule_details_router的核心功能，包括CRUD接口、参数验证、错误处理等
"""

import json
import pytest
from unittest.mock import Mock, patch, MagicMock
from fastapi.testclient import TestClient
from fastapi import HTTPException

from api.routers.master.rule_details import rule_details_router
from models.database import RuleDetail, RuleDetailStatusEnum, RuleTemplate, RuleTemplateStatusEnum
from services.rule_detail_service import RuleDetailService, ServiceError


class TestRuleDetailsRouter:
    """规则详情路由测试类
    
    测试范围：
    - GET接口测试
    - POST接口测试
    - PUT接口测试
    - DELETE接口测试
    - 参数验证
    - 错误处理
    - 响应格式
    """

    @pytest.fixture
    def mock_session(self):
        """模拟数据库会话"""
        return Mock()

    @pytest.fixture
    def mock_template(self):
        """模拟规则模板"""
        template = Mock(spec=RuleTemplate)
        template.rule_key = "test_rule"
        template.name = "测试规则模板"
        template.status = RuleTemplateStatusEnum.READY
        return template

    @pytest.fixture
    def mock_rule_detail(self):
        """模拟规则明细"""
        detail = Mock(spec=RuleDetail)
        detail.id = 1
        detail.rule_id = "TEST_001"
        detail.rule_key = "test_rule"
        detail.rule_name = "测试规则"
        detail.level1 = "数据质量"
        detail.level2 = "完整性"
        detail.level3 = "必填字段"
        detail.error_reason = "字段不能为空"
        detail.degree = "严重"
        detail.reference = "数据质量管理规范"
        detail.detail_position = "患者基本信息"
        detail.prompted_fields1 = "patient_name"
        detail.type = "数据校验"
        detail.pos = "住院"
        detail.applicableArea = "全国"
        detail.default_use = "是"
        detail.start_date = "2024-01-01"
        detail.end_date = "2024-12-31"
        detail.status = RuleDetailStatusEnum.ACTIVE
        detail.created_at = "2024-01-01T00:00:00"
        detail.updated_at = "2024-01-01T00:00:00"
        return detail

    @pytest.fixture
    def test_client(self, mock_dependencies):
        """创建测试客户端"""
        from fastapi import FastAPI
        from api.routers.master.rule_details import rule_details_router
        from core.db_session import get_db_session
        
        # 创建FastAPI应用并覆盖依赖
        app = FastAPI()
        app.include_router(rule_details_router)
        
        # 覆盖数据库依赖
        app.dependency_overrides[get_db_session] = lambda: mock_dependencies
        
        return TestClient(app)
    
    @pytest.fixture(autouse=True)
    def mock_dependencies(self):
        """自动Mock数据库和认证依赖"""
        with patch('config.settings.settings') as mock_settings:
            mock_session = Mock()
            # Mock settings 让认证使用开发密钥
            mock_settings.MASTER_API_SECRET_KEY = "a_very_secret_key_for_development"
            yield mock_session

    def test_get_rule_details_with_valid_params_should_return_success(self, test_client, mock_dependencies, mock_template):
        """使用有效参数获取规则明细列表应该返回成功"""
        # Arrange
        mock_rule_details = [Mock(spec=RuleDetail) for _ in range(3)]
        for i, detail in enumerate(mock_rule_details):
            detail.id = i + 1
            detail.rule_id = f"TEST_{i+1:03d}"
            detail.rule_key = "test_rule"
            detail.rule_name = f"测试规则{i+1}"
            detail.to_dict.return_value = {
                "id": detail.id,
                "rule_id": detail.rule_id,
                "rule_key": detail.rule_key,
                "rule_name": detail.rule_name
            }

        # 设置mock_rule_details的真实属性值
        for i, detail in enumerate(mock_rule_details):
            # 为Mock对象设置真实的属性值而不是Mock
            detail.id = i + 1
            detail.rule_id = f"TEST_{i+1:03d}"
            detail.rule_key = "test_rule"
            detail.rule_name = f"测试规则{i+1}"
            detail.level1 = "数据质量"
            detail.level2 = "完整性"
            detail.level3 = "必填字段"
            detail.error_reason = "字段不能为空"
            detail.degree = "严重"
            detail.reference = "数据质量管理规范"
            detail.detail_position = "患者基本信息"
            detail.prompted_fields1 = "patient_name"
            detail.prompted_fields3 = ""
            detail.type = "数据校验"
            detail.pos = "住院"
            detail.applicableArea = "全国"
            detail.default_use = "是"
            detail.remarks = ""
            detail.in_illustration = ""
            detail.start_date = "2024-01-01"
            detail.end_date = "2024-12-31"
            detail.yb_code = []
            detail.diag_whole_code = []
            detail.diag_code_prefix = []
            detail.diag_name_keyword = ""
            detail.fee_whole_code = []
            detail.fee_code_prefix = []
            detail.extended_fields = ""
            detail.status = Mock()
            detail.status.value = "ACTIVE"
            detail.created_at = Mock()
            detail.created_at.isoformat.return_value = "2024-01-01T00:00:00"
            detail.updated_at = Mock()
            detail.updated_at.isoformat.return_value = "2024-01-01T00:00:00"

        # Mock database queries - 需要模拟两个不同的查询：模板查询和明细查询
        mock_template_query = Mock()
        mock_template_query.filter.return_value = mock_template_query
        mock_template_query.first.return_value = mock_template
        
        mock_detail_query = Mock() 
        mock_detail_query.filter.return_value = mock_detail_query
        mock_detail_query.order_by.return_value = mock_detail_query
        mock_detail_query.offset.return_value = mock_detail_query
        mock_detail_query.limit.return_value = mock_detail_query
        mock_detail_query.all.return_value = mock_rule_details
        mock_detail_query.count.return_value = 10
        
        # 根据查询的模型类返回不同的query对象
        def mock_query_side_effect(model_class):
            if model_class.__name__ == 'RuleTemplate':
                return mock_template_query
            elif model_class.__name__ == 'RuleDetail':
                return mock_detail_query
            return Mock()
        
        mock_dependencies.query.side_effect = mock_query_side_effect

        # Act
        headers = {"X-API-KEY": "a_very_secret_key_for_development"}
        response = test_client.get("/api/v1/rules/details/test_rule?page=1&page_size=3", headers=headers)

        # Assert
        assert response.status_code == 200, f"期望状态码200，实际为{response.status_code}，响应内容：{response.text}"
        data = response.json()
        assert data["success"] is True
        assert data["code"] == 200
        assert "data" in data
        assert data["data"]["total"] == 10
        assert len(data["data"]["items"]) == 3

    def test_get_rule_details_with_nonexistent_rule_should_return_error(self, test_client, mock_dependencies):
        """获取不存在规则的明细应该返回错误"""
        # Arrange
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None  # 规则模板不存在
        mock_dependencies.query.return_value = mock_query

        # Act
        headers = {"X-API-KEY": "a_very_secret_key_for_development"}
        response = test_client.get("/api/v1/rules/details/nonexistent_rule", headers=headers)

        # Assert
        assert response.status_code == 200
        data = response.json()
        # API使用HTTPException，响应结构为 {"detail": {"success": false, ...}}
        assert "detail" in data
        detail = data["detail"]
        assert detail["success"] is False
        assert "不存在" in detail["message"]

    def test_get_rule_detail_with_valid_id_should_return_detail(self, test_client, mock_dependencies, mock_template, mock_rule_detail):
        """使用有效ID获取单条规则明细应该返回详情"""
        # Arrange
        # 设置 mock_rule_detail 的所有必需属性
        mock_rule_detail.id = 1
        mock_rule_detail.rule_id = "TEST_001"
        mock_rule_detail.rule_key = "test_rule"
        mock_rule_detail.rule_name = "测试规则"
        mock_rule_detail.level1 = "数据质量"
        mock_rule_detail.level2 = "完整性"
        mock_rule_detail.level3 = "必填字段"
        mock_rule_detail.error_reason = "字段不能为空"
        mock_rule_detail.degree = "严重"
        mock_rule_detail.reference = "数据质量管理规范"
        mock_rule_detail.detail_position = "患者基本信息"
        mock_rule_detail.prompted_fields3 = ""
        mock_rule_detail.prompted_fields1 = "patient_name"
        mock_rule_detail.type = "数据校验"
        mock_rule_detail.pos = "住院"
        mock_rule_detail.applicableArea = "全国"
        mock_rule_detail.default_use = "是"
        mock_rule_detail.remarks = ""
        mock_rule_detail.in_illustration = ""
        mock_rule_detail.start_date = "2024-01-01"
        mock_rule_detail.end_date = "2024-12-31"
        mock_rule_detail.yb_code = []
        mock_rule_detail.diag_whole_code = []
        mock_rule_detail.diag_code_prefix = []
        mock_rule_detail.diag_name_keyword = ""
        mock_rule_detail.fee_whole_code = []
        mock_rule_detail.fee_code_prefix = []
        mock_rule_detail.extended_fields = ""
        # Mock datetime 对象，具有 isoformat 方法
        mock_rule_detail.status = Mock()
        mock_rule_detail.status.value = "ACTIVE"
        mock_rule_detail.created_at = Mock()
        mock_rule_detail.created_at.isoformat.return_value = "2024-01-01T00:00:00"
        mock_rule_detail.updated_at = Mock()
        mock_rule_detail.updated_at.isoformat.return_value = "2024-01-01T00:00:00"

        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.side_effect = [mock_template, mock_rule_detail]  # 第一次返回模板，第二次返回明细
        mock_dependencies.query.return_value = mock_query

        # Act
        headers = {"X-API-KEY": "a_very_secret_key_for_development"}
        response = test_client.get("/api/v1/rules/details/test_rule/TEST_001", headers=headers)

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["rule_id"] == "TEST_001"

    def test_get_rule_detail_with_nonexistent_id_should_return_error(self, test_client, mock_dependencies, mock_template):
        """获取不存在的规则明细应该返回错误"""
        # Arrange
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.side_effect = [mock_template, None]  # 模板存在，明细不存在
        mock_dependencies.query.return_value = mock_query

        # Act
        headers = {"X-API-KEY": "a_very_secret_key_for_development"}
        response = test_client.get("/api/v1/rules/details/test_rule/NONEXISTENT", headers=headers)

        # Assert
        assert response.status_code == 200
        data = response.json()
        # API使用HTTPException，响应结构为 {"detail": {"success": false, ...}}
        assert "detail" in data
        detail = data["detail"]
        assert detail["success"] is False
        assert "不存在" in detail["message"]

    def test_get_rule_details_statistics_should_return_stats(self, test_client, mock_dependencies, mock_template):
        """获取规则明细统计应该返回统计信息"""
        # Arrange
        mock_stats = {
            "rule_key": "test_rule",
            "total_count": 100,
            "status_distribution": {"ACTIVE": 80, "INACTIVE": 20},
            "level1_distribution": {"数据质量": 60, "业务规则": 40}
        }

        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_template
        mock_dependencies.query.return_value = mock_query

        with patch('api.routers.master.rule_details.RuleDetailService') as mock_service_class:
            mock_service = Mock()
            mock_service.get_rule_detail_statistics.return_value = mock_stats
            mock_service_class.return_value = mock_service

            # Act
            headers = {"X-API-KEY": "a_very_secret_key_for_development"}
            response = test_client.get("/api/v1/rules/details/test_rule/statistics", headers=headers)

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["total_count"] == 100
        assert data["data"]["status_distribution"]["ACTIVE"] == 80

    def test_create_rule_detail_with_valid_data_should_succeed(self, test_client, mock_dependencies, mock_template, mock_rule_detail):
        """使用有效数据创建规则明细应该成功"""
        # Arrange
        create_data = {
            "rule_id": "TEST_001",
            "rule_name": "测试规则",
            "level1": "数据质量",
            "level2": "完整性",
            "level3": "必填字段",
            "error_reason": "字段不能为空",
            "degree": "严重",
            "reference": "数据质量管理规范",
            "detail_position": "患者基本信息",
            "prompted_fields1": "patient_name",
            "type": "数据校验",
            "pos": "住院",
            "applicableArea": "全国",
            "default_use": "是",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31"
        }

        # 设置mock_rule_detail的完整属性，包括datetime对象
        mock_rule_detail.id = 1
        mock_rule_detail.rule_id = "TEST_001"
        mock_rule_detail.rule_key = "test_rule"
        mock_rule_detail.rule_name = "测试规则"
        mock_rule_detail.level1 = "数据质量"
        mock_rule_detail.level2 = "完整性"
        mock_rule_detail.level3 = "必填字段"
        mock_rule_detail.error_reason = "字段不能为空"
        mock_rule_detail.degree = "严重"
        mock_rule_detail.reference = "数据质量管理规范"
        mock_rule_detail.detail_position = "患者基本信息"
        mock_rule_detail.prompted_fields3 = ""
        mock_rule_detail.prompted_fields1 = "patient_name"
        mock_rule_detail.type = "数据校验"
        mock_rule_detail.pos = "住院"
        mock_rule_detail.applicableArea = "全国"
        mock_rule_detail.default_use = "是"
        mock_rule_detail.remarks = ""
        mock_rule_detail.in_illustration = ""
        mock_rule_detail.start_date = "2024-01-01"
        mock_rule_detail.end_date = "2024-12-31"
        mock_rule_detail.yb_code = []
        mock_rule_detail.diag_whole_code = []
        mock_rule_detail.diag_code_prefix = []
        mock_rule_detail.diag_name_keyword = ""
        mock_rule_detail.fee_whole_code = []
        mock_rule_detail.fee_code_prefix = []
        mock_rule_detail.extended_fields = ""
        # Mock datetime 对象，具有 isoformat 方法
        mock_rule_detail.status = Mock()
        mock_rule_detail.status.value = "ACTIVE"
        mock_rule_detail.created_at = Mock()
        mock_rule_detail.created_at.isoformat.return_value = "2024-01-01T00:00:00"
        mock_rule_detail.updated_at = Mock()
        mock_rule_detail.updated_at.isoformat.return_value = "2024-01-01T00:00:00"

        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_template
        mock_dependencies.query.return_value = mock_query

        with patch('api.routers.master.rule_details._get_mapping_engine') as mock_mapping, \
             patch('api.routers.master.rule_details.RuleDetail') as mock_rule_class:
            
            # Mock mapping engine
            mock_engine = Mock()
            mock_engine.validate_data.return_value = Mock(valid=True, errors=[])
            mock_engine.normalize_field_names.return_value = create_data
            mock_engine.separate_fields.return_value = (create_data, {})
            mock_mapping.return_value = mock_engine
            
            # Mock RuleDetail creation
            mock_rule_class.return_value = mock_rule_detail
            
            # Act
            headers = {"X-API-KEY": "a_very_secret_key_for_development"}
            response = test_client.post("/api/v1/rules/details/test_rule", json=create_data, headers=headers)

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["rule_id"] == "TEST_001"

    def test_create_rule_detail_with_invalid_data_should_fail(self, test_client, mock_session, mock_template):
        """使用无效数据创建规则明细应该失败"""
        # Arrange
        invalid_data = {
            "rule_id": "",  # 空的rule_id
            "rule_name": "测试规则"
        }

        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_template
        mock_session.query.return_value = mock_query

        with patch('api.routers.master.rule_details.get_db_session', return_value=mock_session), \
             patch('api.routers.master.rule_details.get_api_key_dependency', return_value=lambda: None), \
             patch('api.routers.master.rule_details.RuleDetailService') as mock_service_class:
            
            mock_service = Mock()
            mock_service.create_rule_detail.side_effect = ServiceError("数据验证失败")
            mock_service_class.return_value = mock_service

            # Act
            headers = {"X-API-KEY": "a_very_secret_key_for_development"}
            response = test_client.post("/api/v1/rules/details/test_rule", json=invalid_data, headers=headers)

        # Assert
        # FastAPI 在数据验证失败时返回422状态码
        assert response.status_code == 422
        data = response.json()
        # 422响应格式不同，直接包含detail
        assert "detail" in data

    def test_update_rule_detail_with_valid_data_should_succeed(self, test_client, mock_dependencies, mock_template):
        """使用有效数据更新规则明细应该成功"""
        # Arrange
        update_data = {
            "rule_name": "更新后的规则名称",
            "degree": "中等"
        }

        # 创建完整的mock_rule_detail对象
        mock_rule_detail = Mock(spec=RuleDetail)
        # 设置所有必需的属性为真实值，不是Mock
        mock_rule_detail.id = 1
        mock_rule_detail.rule_id = "TEST_001"
        mock_rule_detail.rule_key = "test_rule"
        mock_rule_detail.rule_name = "更新后的规则名称"
        mock_rule_detail.level1 = "数据质量"
        mock_rule_detail.level2 = "完整性"
        mock_rule_detail.level3 = "必填字段"
        mock_rule_detail.error_reason = "字段不能为空"
        mock_rule_detail.degree = "中等"
        mock_rule_detail.reference = "数据质量管理规范"
        mock_rule_detail.detail_position = "患者基本信息"
        mock_rule_detail.prompted_fields3 = ""
        mock_rule_detail.prompted_fields1 = "patient_name"
        mock_rule_detail.type = "数据校验"
        mock_rule_detail.pos = "住院"
        mock_rule_detail.applicableArea = "全国"
        mock_rule_detail.default_use = "是"
        mock_rule_detail.remarks = ""
        mock_rule_detail.in_illustration = ""
        mock_rule_detail.start_date = "2024-01-01"
        mock_rule_detail.end_date = "2024-12-31"
        mock_rule_detail.yb_code = []
        mock_rule_detail.diag_whole_code = []
        mock_rule_detail.diag_code_prefix = []
        mock_rule_detail.diag_name_keyword = ""
        mock_rule_detail.fee_whole_code = []
        mock_rule_detail.fee_code_prefix = []
        mock_rule_detail.extended_fields = ""
        # Mock datetime 对象，具有 isoformat 方法
        mock_rule_detail.status = Mock()
        mock_rule_detail.status.value = "ACTIVE"
        mock_rule_detail.created_at = Mock()
        mock_rule_detail.created_at.isoformat.return_value = "2024-01-01T00:00:00"
        mock_rule_detail.updated_at = Mock()
        mock_rule_detail.updated_at.isoformat.return_value = "2024-01-01T00:00:00"

        # Mock database queries - 需要返回两次查询：第一次模板查询，第二次明细查询
        mock_template_query = Mock()
        mock_template_query.filter.return_value = mock_template_query
        mock_template_query.first.return_value = mock_template
        
        mock_detail_query = Mock()
        mock_detail_query.filter.return_value = mock_detail_query
        mock_detail_query.first.return_value = mock_rule_detail
        
        # Mock更新操作的session操作
        with patch('api.routers.master.rule_details._get_mapping_engine') as mock_mapping:
            # Mock mapping engine
            mock_engine = Mock()
            mock_engine.validate_data.return_value = Mock(valid=True, errors=[])
            mock_engine.normalize_field_names.return_value = update_data
            mock_engine.separate_fields.return_value = (update_data, {})
            mock_mapping.return_value = mock_engine
            
            # 设置查询链式调用
            def mock_query_side_effect(model_class):
                if model_class.__name__ == 'RuleTemplate':
                    return mock_template_query
                elif model_class.__name__ == 'RuleDetail':
                    return mock_detail_query
                return Mock()
            
            mock_dependencies.query.side_effect = mock_query_side_effect

            # Act
            headers = {"X-API-KEY": "a_very_secret_key_for_development"}
            response = test_client.put("/api/v1/rules/details/test_rule/TEST_001", json=update_data, headers=headers)

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["rule_name"] == "更新后的规则名称"

    def test_delete_rule_detail_should_succeed(self, test_client, mock_session, mock_template):
        """删除规则明细应该成功"""
        # Arrange
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_template
        mock_session.query.return_value = mock_query

        with patch('api.routers.master.rule_details.get_db_session', return_value=mock_session), \
             patch('api.routers.master.rule_details.get_api_key_dependency', return_value=lambda: None), \
             patch('api.routers.master.rule_details.RuleDetailService') as mock_service_class:
            
            mock_service = Mock()
            mock_service.delete_rule_detail.return_value = True
            mock_service_class.return_value = mock_service

            # Act
            headers = {"X-API-KEY": "a_very_secret_key_for_development"}
            response = test_client.delete("/api/v1/rules/details/test_rule/TEST_001", headers=headers)

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "删除成功" in data["message"]

    def test_batch_operation_create_should_succeed(self, test_client, mock_dependencies, mock_template):
        """批量创建操作应该成功"""
        # Arrange
        batch_data = {
            "operation": "CREATE",  # 使用大写，与API路由中的检查一致
            "data": [
                {"rule_id": "TEST_001", "rule_name": "规则1"},
                {"rule_id": "TEST_002", "rule_name": "规则2"}
            ]
        }

        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_template
        mock_dependencies.query.return_value = mock_query

        with patch('api.routers.master.rule_details._get_mapping_engine') as mock_mapping:
            # Mock mapping engine
            mock_engine = Mock()
            mock_engine.validate_data.return_value = Mock(valid=True, errors=[])
            mock_engine.normalize_field_names.return_value = {"rule_id": "TEST_001", "rule_name": "规则1"}
            mock_engine.separate_fields.return_value = ({"rule_id": "TEST_001", "rule_name": "规则1", "rule_key": "test_rule"}, {})
            mock_mapping.return_value = mock_engine

            # Act
            headers = {"X-API-KEY": "a_very_secret_key_for_development"}
            response = test_client.post("/api/v1/rules/details/test_rule/batch", json=batch_data, headers=headers)

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["success_count"] == 2
