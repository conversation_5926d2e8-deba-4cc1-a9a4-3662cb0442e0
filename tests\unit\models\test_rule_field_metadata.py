"""
规则字段元数据模型单元测试
测试RuleFieldMetadata模型的核心功能，包括创建、验证、序列化和关联关系
"""

import json
import pytest
from sqlalchemy.exc import IntegrityError

from models.database import RuleFieldMetadata, FieldTypeEnum, RuleTemplate, RuleTemplateStatusEnum


class TestRuleFieldMetadata:
    """规则字段元数据模型测试类

    测试范围：
    - 模型创建和基本属性
    - 序列化和反序列化
    - 字段验证功能
    - 验证规则解析
    - 默认值处理
    - 关联关系
    """

    @pytest.fixture
    def sample_template(self, test_db_session):
        """创建示例规则模板"""
        # 先检查是否已存在，避免重复创建
        existing = test_db_session.query(RuleTemplate).filter_by(rule_key="metadata_test_template").first()
        if existing:
            return existing

        template = RuleTemplate(
            rule_key="metadata_test_template",
            rule_type="元数据测试规则类型",
            name="元数据测试规则模板",
            status=RuleTemplateStatusEnum.READY,
        )
        test_db_session.add(template)
        test_db_session.commit()
        return template

    def test_create_field_metadata_with_valid_data_should_succeed(self, test_db_session, sample_template):
        """使用有效数据创建字段元数据应该成功"""
        # Arrange
        metadata_data = {
            "rule_key": sample_template.rule_key,
            "field_name": "patient_name",
            "field_type": FieldTypeEnum.string,
            "is_required": True,
            "is_fixed_field": True,
            "display_name": "患者姓名",
            "description": "患者的真实姓名",
            "validation_rule": '["required", "max_length:50"]',
            "default_value": "",
            "excel_column_order": 1,
        }

        # Act
        metadata = RuleFieldMetadata(**metadata_data)
        test_db_session.add(metadata)
        test_db_session.commit()

        # Assert
        assert metadata.id is not None
        assert metadata.rule_key == sample_template.rule_key
        assert metadata.field_name == "patient_name"
        assert metadata.field_type == FieldTypeEnum.string
        assert metadata.is_required is True
        assert metadata.is_fixed_field is True
        assert metadata.display_name == "患者姓名"
        assert metadata.description == "患者的真实姓名"
        assert metadata.excel_column_order == 1
        assert metadata.created_at is not None

    def test_create_field_metadata_with_minimal_data_should_succeed(self, test_db_session, sample_template):
        """使用最小必填数据创建字段元数据应该成功"""
        # Arrange
        metadata_data = {
            "rule_key": sample_template.rule_key,
            "field_name": "simple_field",
            "field_type": FieldTypeEnum.string,
            "display_name": "简单字段",
        }

        # Act
        metadata = RuleFieldMetadata(**metadata_data)
        test_db_session.add(metadata)
        test_db_session.commit()

        # Assert
        assert metadata.id is not None
        assert metadata.field_name == "simple_field"
        assert metadata.is_required is False  # 默认值
        assert metadata.is_fixed_field is False  # 默认值
        assert metadata.description is None
        assert metadata.validation_rule is None
        assert metadata.default_value is None
        assert metadata.excel_column_order is None

    def test_create_duplicate_field_name_should_fail(self, test_db_session, sample_template):
        """创建重复字段名的元数据应该失败"""
        # Arrange
        metadata1 = RuleFieldMetadata(
            rule_key=sample_template.rule_key,
            field_name="duplicate_field",
            field_type=FieldTypeEnum.string,
            display_name="重复字段1",
        )
        metadata2 = RuleFieldMetadata(
            rule_key=sample_template.rule_key,
            field_name="duplicate_field",  # 重复的字段名
            field_type=FieldTypeEnum.integer,
            display_name="重复字段2",
        )

        # Act & Assert
        test_db_session.add(metadata1)
        test_db_session.commit()

        test_db_session.add(metadata2)
        with pytest.raises(IntegrityError):
            test_db_session.commit()

    def test_to_dict_should_return_correct_format(self, test_db_session, sample_template):
        """to_dict方法应该返回正确的字典格式"""
        # Arrange
        metadata = RuleFieldMetadata(
            rule_key=sample_template.rule_key,
            field_name="dict_test_field",
            field_type=FieldTypeEnum.integer,
            is_required=True,
            is_fixed_field=False,
            display_name="字典测试字段",
            description="测试to_dict方法",
            validation_rule='["required", "min:0", "max:100"]',
            default_value="0",
            excel_column_order=5,
        )
        test_db_session.add(metadata)
        test_db_session.commit()

        # Act
        result = metadata.to_dict()

        # Assert
        assert isinstance(result, dict)
        assert result["rule_key"] == sample_template.rule_key
        assert result["field_name"] == "dict_test_field"
        assert result["field_type"] == "integer"
        assert result["is_required"] is True
        assert result["is_fixed_field"] is False
        assert result["display_name"] == "字典测试字段"
        assert result["description"] == "测试to_dict方法"
        assert result["validation_rule"] == '["required", "min:0", "max:100"]'
        assert result["default_value"] == "0"
        assert result["excel_column_order"] == 5
        assert "id" in result
        assert "created_at" in result

    def test_from_dict_should_create_valid_instance(self):
        """from_dict方法应该创建有效的实例"""
        # Arrange
        data = {
            "rule_key": "metadata_test_template",  # 保持字符串用于测试from_dict
            "field_name": "from_dict_field",
            "field_type": "boolean",
            "is_required": False,
            "is_fixed_field": True,
            "display_name": "从字典创建的字段",
            "description": "测试from_dict方法",
            "validation_rule": '["required"]',
            "default_value": "false",
            "excel_column_order": 3,
        }

        # Act
        metadata = RuleFieldMetadata.from_dict(data)

        # Assert
        assert metadata.rule_key == "metadata_test_template"
        assert metadata.field_name == "from_dict_field"
        assert metadata.field_type == FieldTypeEnum.boolean
        assert metadata.is_required is False
        assert metadata.is_fixed_field is True
        assert metadata.display_name == "从字典创建的字段"

    def test_get_validation_rules_should_parse_json_correctly(self, test_db_session, sample_template):
        """获取验证规则应该正确解析JSON"""
        # Arrange
        validation_rules = ["required", "max_length:100", "min:1"]
        metadata = RuleFieldMetadata(
            rule_key="metadata_test_template",
            field_name="validation_test_field",
            field_type=FieldTypeEnum.string,
            display_name="验证测试字段",
            validation_rule=json.dumps(validation_rules),
        )
        test_db_session.add(metadata)
        test_db_session.commit()

        # Act
        result = metadata.get_validation_rules()

        # Assert
        assert result == validation_rules
        assert "required" in result
        assert "max_length:100" in result
        assert "min:1" in result

    def test_get_validation_rules_with_string_rule_should_return_list(self, test_db_session, sample_template):
        """单个字符串验证规则应该返回列表"""
        # Arrange
        metadata = RuleFieldMetadata(
            rule_key="metadata_test_template",
            field_name="string_rule_field",
            field_type=FieldTypeEnum.string,
            display_name="字符串规则字段",
            validation_rule='"required"',  # 单个字符串规则
        )
        test_db_session.add(metadata)
        test_db_session.commit()

        # Act
        result = metadata.get_validation_rules()

        # Assert
        assert result == ["required"]

    def test_get_validation_rules_with_invalid_json_should_return_empty_list(self, test_db_session, sample_template):
        """无效JSON验证规则应该返回空列表"""
        # Arrange
        metadata = RuleFieldMetadata(
            rule_key="metadata_test_template",
            field_name="invalid_json_field",
            field_type=FieldTypeEnum.string,
            display_name="无效JSON字段",
            validation_rule="invalid json",  # 无效JSON
        )
        test_db_session.add(metadata)
        test_db_session.commit()

        # Act
        result = metadata.get_validation_rules()

        # Assert
        assert result == []

    def test_validate_field_value_required_field_should_check_correctly(self, test_db_session, sample_template):
        """验证必填字段应该正确检查"""
        # Arrange
        metadata = RuleFieldMetadata(
            rule_key="metadata_test_template",
            field_name="required_field",
            field_type=FieldTypeEnum.string,
            is_required=True,
            display_name="必填字段",
        )
        test_db_session.add(metadata)
        test_db_session.commit()

        # Act & Assert
        # 空值应该失败
        result = metadata.validate_field_value(None)
        assert result["valid"] is False
        assert "必填字段不能为空" in result["errors"]

        result = metadata.validate_field_value("")
        assert result["valid"] is False
        assert "必填字段不能为空" in result["errors"]

        # 有值应该成功
        result = metadata.validate_field_value("有效值")
        assert result["valid"] is True
        assert len(result["errors"]) == 0

    def test_validate_field_value_integer_type_should_check_correctly(self, test_db_session, sample_template):
        """验证整数类型字段应该正确检查"""
        # Arrange
        metadata = RuleFieldMetadata(
            rule_key="metadata_test_template",
            field_name="integer_field",
            field_type=FieldTypeEnum.integer,
            display_name="整数字段",
        )
        test_db_session.add(metadata)
        test_db_session.commit()

        # Act & Assert
        # 有效整数
        result = metadata.validate_field_value(123)
        assert result["valid"] is True

        result = metadata.validate_field_value("456")
        assert result["valid"] is True

        # 无效整数
        result = metadata.validate_field_value("not_a_number")
        assert result["valid"] is False
        assert "整数字段必须为整数" in result["errors"]

    def test_validate_field_value_with_validation_rules_should_apply_rules(self, test_db_session, sample_template):
        """验证字段值应该应用验证规则"""
        # Arrange
        validation_rules = ["max_length:10", "min:5", "max:100"]
        metadata = RuleFieldMetadata(
            rule_key="metadata_test_template",
            field_name="rules_field",
            field_type=FieldTypeEnum.string,
            display_name="规则字段",
            validation_rule=json.dumps(validation_rules),
        )
        test_db_session.add(metadata)
        test_db_session.commit()

        # Act & Assert
        # 超过最大长度
        result = metadata.validate_field_value("这是一个超过十个字符的长字符串")
        assert result["valid"] is False
        assert "规则字段长度不能超过10个字符" in result["errors"]

        # 有效长度
        result = metadata.validate_field_value("有效字符串")
        assert result["valid"] is True

    def test_get_default_value_parsed_should_parse_by_type(self, test_db_session, sample_template):
        """获取解析后的默认值应该根据类型解析"""
        # Arrange & Act & Assert

        # 整数类型
        metadata_int = RuleFieldMetadata(
            rule_key="metadata_test_template",
            field_name="int_field",
            field_type=FieldTypeEnum.integer,
            display_name="整数字段",
            default_value="42",
        )
        test_db_session.add(metadata_int)
        assert metadata_int.get_default_value_parsed() == 42

        # 布尔类型
        metadata_bool = RuleFieldMetadata(
            rule_key="metadata_test_template",
            field_name="bool_field",
            field_type=FieldTypeEnum.boolean,
            display_name="布尔字段",
            default_value="true",
        )
        test_db_session.add(metadata_bool)
        assert metadata_bool.get_default_value_parsed() is True

        # 数组类型
        metadata_array = RuleFieldMetadata(
            rule_key="metadata_test_template",
            field_name="array_field",
            field_type=FieldTypeEnum.array,
            display_name="数组字段",
            default_value='["item1", "item2"]',
        )
        test_db_session.add(metadata_array)
        assert metadata_array.get_default_value_parsed() == ["item1", "item2"]

        # 字符串类型
        metadata_str = RuleFieldMetadata(
            rule_key="metadata_test_template",
            field_name="str_field",
            field_type=FieldTypeEnum.string,
            display_name="字符串字段",
            default_value="默认字符串",
        )
        test_db_session.add(metadata_str)
        assert metadata_str.get_default_value_parsed() == "默认字符串"

        test_db_session.commit()

    def test_relationship_with_template_should_work_correctly(self, test_db_session, sample_template):
        """与模板的关联关系应该正常工作"""
        # Arrange
        metadata = RuleFieldMetadata(
            rule_key="metadata_test_template",
            field_name="relationship_field",
            field_type=FieldTypeEnum.string,
            display_name="关联关系字段",
        )
        test_db_session.add(metadata)
        test_db_session.commit()

        # Act & Assert
        assert metadata.template is not None
        assert metadata.template.rule_key == "metadata_test_template"
        assert metadata.template.name == "元数据测试规则模板"

    def test_validate_field_value_with_invalid_validation_rules_should_add_warnings(
        self, test_db_session, sample_template
    ):
        """无效验证规则应该添加警告"""
        # Arrange
        invalid_rules = ["max_length:invalid", "min:not_a_number"]
        metadata = RuleFieldMetadata(
            rule_key="metadata_test_template",
            field_name="warning_field",
            field_type=FieldTypeEnum.string,
            display_name="警告字段",
            validation_rule=json.dumps(invalid_rules),
        )
        test_db_session.add(metadata)
        test_db_session.commit()

        # Act
        result = metadata.validate_field_value("test_value")

        # Assert
        assert result["valid"] is True  # 值本身是有效的
        assert len(result["warnings"]) > 0  # 但有警告
        assert any("无效的验证规则" in warning for warning in result["warnings"])
