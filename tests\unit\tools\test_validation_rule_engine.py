"""
验证规则引擎单元测试
测试ValidationRuleEngine的核心功能，包括规则加载、数据验证、缓存机制等
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime

from services.validation_rule_engine import (
    ValidationRuleEngine,
    ValidationRule,
    ValidationError,
    ValidationResult,
    ValidationRuleType,
    ServiceError,
)
from models.database import RuleFieldMetadata, FieldTypeEnum


class TestValidationRuleEngine:
    """验证规则引擎测试类

    测试范围：
    - 规则加载和缓存
    - 数据验证功能
    - 错误处理
    - 性能优化
    """

    @pytest.fixture
    def mock_session(self):
        """模拟数据库会话"""
        return Mock()

    @pytest.fixture
    def sample_field_metadata(self):
        """示例字段元数据"""
        metadata1 = Mock(spec=RuleFieldMetadata)
        metadata1.field_name = "patient_name"
        metadata1.display_name = "患者姓名"
        metadata1.field_type = FieldTypeEnum.string
        metadata1.is_required = True
        metadata1.validation_rule = '["required", "max_length:50"]'
        metadata1.excel_column_order = 1

        metadata2 = Mock(spec=RuleFieldMetadata)
        metadata2.field_name = "age"
        metadata2.display_name = "年龄"
        metadata2.field_type = FieldTypeEnum.integer
        metadata2.is_required = True
        metadata2.validation_rule = '["required", "min_value:0", "max_value:150"]'
        metadata2.excel_column_order = 2

        metadata3 = Mock(spec=RuleFieldMetadata)
        metadata3.field_name = "email"
        metadata3.display_name = "邮箱"
        metadata3.field_type = FieldTypeEnum.string
        metadata3.is_required = False
        metadata3.validation_rule = '["email"]'
        metadata3.excel_column_order = 3

        return [metadata1, metadata2, metadata3]

    @pytest.fixture
    def validation_engine(self, mock_session):
        """创建验证规则引擎实例"""
        return ValidationRuleEngine(mock_session)

    def test_init_should_initialize_correctly(self, mock_session):
        """初始化应该正确设置属性"""
        # Act
        engine = ValidationRuleEngine(mock_session)

        # Assert
        assert engine.session == mock_session
        assert engine.cache_ttl == 300  # 默认值
        assert engine._rule_cache == {}
        assert engine._cache_timestamp == {}

    def test_load_validation_rules_should_load_rules_correctly(
        self, validation_engine, mock_session, sample_field_metadata
    ):
        """加载验证规则应该正确加载规则"""
        # Arrange
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = sample_field_metadata
        mock_session.query.return_value = mock_query

        # Act
        rules = validation_engine.get_validation_rules("test_rule_key")

        # Assert
        assert len(rules) > 0
        assert any(rule.field_name == "patient_name" for rule in rules)
        assert any(rule.field_name == "age" for rule in rules)
        assert any(rule.field_name == "email" for rule in rules)

    def test_load_validation_rules_should_cache_results(self, validation_engine, mock_session, sample_field_metadata):
        """加载验证规则应该缓存结果"""
        # Arrange
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = sample_field_metadata
        mock_session.query.return_value = mock_query

        # Act
        rules1 = validation_engine.get_validation_rules("test_rule_key")
        rules2 = validation_engine.get_validation_rules("test_rule_key")  # 第二次调用应该使用缓存

        # Assert
        assert len(rules1) == len(rules2)
        assert mock_session.query.call_count == 1  # 只调用一次数据库查询

    def test_validate_data_with_valid_data_should_pass(self, validation_engine, mock_session, sample_field_metadata):
        """验证有效数据应该通过"""
        # Arrange
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = sample_field_metadata
        mock_session.query.return_value = mock_query

        valid_data = {"patient_name": "张三", "age": 30, "email": "<EMAIL>"}

        # Act
        result = validation_engine.validate_data("test_rule_key", valid_data)

        # Assert
        assert result.valid is True
        assert len(result.errors) == 0
        assert result.field_count == 3
        assert "patient_name" in result.validated_fields
        assert "age" in result.validated_fields
        assert "email" in result.validated_fields

    def test_validate_data_with_missing_required_field_should_fail(
        self, validation_engine, mock_session, sample_field_metadata
    ):
        """验证缺少必填字段的数据应该失败"""
        # Arrange
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = sample_field_metadata
        mock_session.query.return_value = mock_query

        invalid_data = {
            "age": 30,
            "email": "<EMAIL>",
            # 缺少必填字段 patient_name
        }

        # Act
        result = validation_engine.validate_data("test_rule_key", invalid_data)

        # Assert
        assert result.valid is False
        assert len(result.errors) > 0
        assert any(error.field_name == "patient_name" for error in result.errors)
        assert any("不能为空" in error.error_message for error in result.errors)

    def test_validate_data_with_invalid_type_should_fail(self, validation_engine, mock_session, sample_field_metadata):
        """验证类型错误的数据应该失败"""
        # Arrange
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = sample_field_metadata
        mock_session.query.return_value = mock_query

        invalid_data = {
            "patient_name": "张三",
            "age": "not_a_number",  # 应该是整数
            "email": "<EMAIL>",
        }

        # Act
        result = validation_engine.validate_data("test_rule_key", invalid_data)

        # Assert
        assert result.valid is False
        assert len(result.errors) > 0
        assert any(error.field_name == "age" for error in result.errors)

    def test_validate_data_with_invalid_email_should_fail(self, validation_engine, mock_session, sample_field_metadata):
        """验证无效邮箱格式应该失败"""
        # Arrange
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = sample_field_metadata
        mock_session.query.return_value = mock_query

        invalid_data = {
            "patient_name": "张三",
            "age": 30,
            "email": "invalid_email_format",  # 无效邮箱格式
        }

        # Act
        result = validation_engine.validate_data("test_rule_key", invalid_data)

        # Assert
        assert result.valid is False
        assert len(result.errors) > 0
        assert any(error.field_name == "email" for error in result.errors)

    def test_validate_data_with_value_out_of_range_should_fail(
        self, validation_engine, mock_session, sample_field_metadata
    ):
        """验证超出范围的值应该失败"""
        # Arrange
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = sample_field_metadata
        mock_session.query.return_value = mock_query

        invalid_data = {
            "patient_name": "张三",
            "age": 200,  # 超出最大值150
            "email": "<EMAIL>",
        }

        # Act
        result = validation_engine.validate_data("test_rule_key", invalid_data)

        # Assert
        assert result.valid is False
        assert len(result.errors) > 0
        assert any(error.field_name == "age" for error in result.errors)

    def test_validate_data_with_too_long_string_should_fail(
        self, validation_engine, mock_session, sample_field_metadata
    ):
        """验证过长字符串应该失败"""
        # Arrange
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = sample_field_metadata
        mock_session.query.return_value = mock_query

        invalid_data = {
            "patient_name": "这是一个非常非常非常非常非常非常非常非常非常非常非常非常非常非常长的患者姓名，超过了50个字符的限制，确实超过了",
            "age": 30,
            "email": "<EMAIL>",
        }

        # Act
        result = validation_engine.validate_data("test_rule_key", invalid_data)

        # Assert
        assert result.valid is False
        assert len(result.errors) > 0
        assert any(error.field_name == "patient_name" for error in result.errors)

    def test_clear_cache_should_clear_all_cache(self, validation_engine):
        """清除缓存应该清除所有缓存"""
        # Arrange
        validation_engine._rule_cache["test_key"] = []
        validation_engine._cache_timestamp["test_key"] = 123456

        # Act
        validation_engine.clear_cache()

        # Assert
        assert len(validation_engine._rule_cache) == 0
        assert len(validation_engine._cache_timestamp) == 0

    def test_get_cache_stats_should_return_correct_stats(self, validation_engine):
        """获取缓存统计应该返回正确的统计信息"""
        # Arrange
        validation_engine._rule_cache["key1"] = []
        validation_engine._rule_cache["key2"] = []

        # Act
        stats = validation_engine.get_cache_stats()

        # Assert
        assert stats["rule_cache_size"] == 2
        assert "cache_ttl" in stats

    def test_validate_data_with_database_error_should_raise_service_error(self, validation_engine, mock_session):
        """数据库错误时应该抛出ServiceError"""
        # Arrange
        mock_session.query.side_effect = Exception("Database connection error")

        # Act & Assert
        with pytest.raises(ServiceError) as exc_info:
            validation_engine.validate_data("test_rule_key", {"field": "value"})

        assert "数据校验失败" in str(exc_info.value)

    def test_cache_expiration_should_reload_rules(self, validation_engine, mock_session, sample_field_metadata):
        """缓存过期应该重新加载规则"""
        # Arrange
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = sample_field_metadata
        mock_session.query.return_value = mock_query

        # 设置很短的缓存TTL
        validation_engine.cache_ttl = 0.001

        # Act
        rules1 = validation_engine.get_validation_rules("test_rule_key")

        # 等待缓存过期
        import time

        time.sleep(0.002)

        rules2 = validation_engine.get_validation_rules("test_rule_key")

        # Assert
        assert len(rules1) == len(rules2)
        assert mock_session.query.call_count == 2  # 应该调用两次数据库查询
