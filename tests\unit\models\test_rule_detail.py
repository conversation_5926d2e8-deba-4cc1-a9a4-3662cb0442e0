"""
规则明细模型单元测试
测试RuleDetail模型的核心功能，包括创建、序列化、扩展字段处理和关联关系
"""

import json
import pytest
from sqlalchemy.exc import IntegrityError

from models.database import RuleDetail, RuleDetailStatusEnum, RuleTemplate, RuleTemplateStatusEnum


class TestRuleDetail:
    """规则明细模型测试类

    测试范围：
    - 模型创建和基本属性
    - 序列化和反序列化
    - 扩展字段处理（JSON字段）
    - 关联关系
    - 约束检查
    """

    @pytest.fixture
    def sample_template(self, test_db_session):
        """创建示例规则模板"""
        # 先检查是否已存在，避免重复创建
        existing = test_db_session.query(RuleTemplate).filter_by(rule_key="test_template").first()
        if existing:
            return existing

        template = RuleTemplate(
            rule_key="test_template", rule_type="测试规则类型", name="测试规则模板", status=RuleTemplateStatusEnum.READY
        )
        test_db_session.add(template)
        test_db_session.commit()
        return template

    def test_create_rule_detail_with_valid_data_should_succeed(self, test_db_session, sample_template):
        """使用有效数据创建规则明细应该成功"""
        # Arrange - 确保模板存在
        assert sample_template.rule_key == "test_template"

        detail_data = {
            "rule_id": "TEST_RULE_001",
            "rule_key": sample_template.rule_key,  # 使用模板的rule_key
            "rule_name": "测试规则明细001",
            "level1": "数据质量",
            "level2": "完整性",
            "level3": "必填字段",
            "error_reason": "字段不能为空",
            "degree": "严重",
            "reference": "数据质量管理规范",
            "detail_position": "患者基本信息",
            "prompted_fields1": "patient_name",
            "type": "数据校验",
            "pos": "住院",
            "applicableArea": "全国",
            "default_use": "是",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
        }

        # Act
        detail = RuleDetail(**detail_data)
        test_db_session.add(detail)
        test_db_session.commit()

        # Assert
        assert detail.id is not None
        assert detail.rule_id == "TEST_RULE_001"
        assert detail.rule_key == sample_template.rule_key
        assert detail.rule_name == "测试规则明细001"
        assert detail.level1 == "数据质量"
        assert detail.level2 == "完整性"
        assert detail.level3 == "必填字段"
        assert detail.status == RuleDetailStatusEnum.ACTIVE  # 默认状态
        assert detail.created_at is not None
        assert detail.updated_at is not None

    def test_create_rule_detail_with_invalid_rule_key_should_fail(self, test_db_session):
        """使用无效rule_key创建规则明细应该失败"""
        # Arrange
        detail_data = {
            "rule_id": "TEST_RULE_002",
            "rule_key": "nonexistent_template",  # 不存在的模板
            "rule_name": "测试规则明细002",
            "level1": "数据质量",
            "level2": "完整性",
            "level3": "必填字段",
            "error_reason": "字段不能为空",
            "degree": "严重",
            "reference": "数据质量管理规范",
            "detail_position": "患者基本信息",
            "prompted_fields1": "patient_name",
            "type": "数据校验",
            "pos": "住院",
            "applicableArea": "全国",
            "default_use": "是",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
        }

        # Act & Assert
        detail = RuleDetail(**detail_data)
        test_db_session.add(detail)
        with pytest.raises(IntegrityError):
            test_db_session.commit()

    def test_to_dict_should_return_correct_format(self, test_db_session, sample_template):
        """to_dict方法应该返回正确的字典格式"""
        # Arrange
        detail = RuleDetail(
            rule_id="DICT_TEST",
            rule_key=sample_template.rule_key,
            rule_name="字典测试规则",
            level1="测试",
            level2="字典",
            level3="转换",
            error_reason="测试原因",
            degree="轻微",
            reference="测试参考",
            detail_position="测试位置",
            prompted_fields1="test_field",
            type="测试类型",
            pos="测试业务",
            applicableArea="测试地区",
            default_use="否",
            start_date="2024-01-01",
            end_date="2024-12-31",
            status=RuleDetailStatusEnum.ACTIVE,
        )
        test_db_session.add(detail)
        test_db_session.commit()

        # Act
        result = detail.to_dict()

        # Assert
        assert isinstance(result, dict)
        assert result["rule_id"] == "DICT_TEST"
        assert result["rule_key"] == sample_template.rule_key
        assert result["rule_name"] == "字典测试规则"
        assert result["level1"] == "测试"
        assert result["level2"] == "字典"
        assert result["level3"] == "转换"
        assert result["status"] == "ACTIVE"
        assert "id" in result
        assert "created_at" in result
        assert "updated_at" in result

    def test_from_dict_should_create_valid_instance(self):
        """from_dict方法应该创建有效的实例"""
        # Arrange
        data = {
            "rule_id": "FROM_DICT_TEST",
            "rule_key": "test_template",  # 这里保持字符串，测试from_dict功能
            "rule_name": "从字典创建的规则",
            "level1": "测试",
            "level2": "创建",
            "level3": "字典",
            "error_reason": "测试原因",
            "degree": "中等",
            "reference": "测试参考",
            "detail_position": "测试位置",
            "prompted_fields1": "test_field",
            "type": "测试类型",
            "pos": "测试业务",
            "applicableArea": "测试地区",
            "default_use": "是",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "status": "INACTIVE",
        }

        # Act
        detail = RuleDetail.from_dict(data)

        # Assert
        assert detail.rule_id == "FROM_DICT_TEST"
        assert detail.rule_key == "test_template"
        assert detail.rule_name == "从字典创建的规则"
        assert detail.level1 == "测试"
        assert detail.status == RuleDetailStatusEnum.INACTIVE

    def test_get_extended_field_should_return_correct_value(self, test_db_session, sample_template):
        """获取扩展字段应该返回正确的值"""
        # Arrange
        extended_data = {"age_threshold": 18, "limit_days": 30, "custom_field": "custom_value"}
        detail = RuleDetail(
            rule_id="EXTENDED_TEST",
            rule_key=sample_template.rule_key,
            rule_name="扩展字段测试",
            level1="测试",
            level2="扩展",
            level3="字段",
            error_reason="测试",
            degree="轻微",
            reference="测试",
            detail_position="测试",
            prompted_fields1="test",
            type="测试",
            pos="测试",
            applicableArea="测试",
            default_use="是",
            start_date="2024-01-01",
            end_date="2024-12-31",
            extended_fields=json.dumps(extended_data, ensure_ascii=False),
        )
        test_db_session.add(detail)
        test_db_session.commit()

        # Act & Assert
        assert detail.get_extended_field("age_threshold") == 18
        assert detail.get_extended_field("limit_days") == 30
        assert detail.get_extended_field("custom_field") == "custom_value"
        assert detail.get_extended_field("nonexistent_field") is None
        assert detail.get_extended_field("nonexistent_field", "default") == "default"

    def test_set_extended_field_should_update_field_correctly(self, test_db_session, sample_template):
        """设置扩展字段应该正确更新字段"""
        # Arrange
        detail = RuleDetail(
            rule_id="SET_EXTENDED_TEST",
            rule_key=sample_template.rule_key,
            rule_name="设置扩展字段测试",
            level1="测试",
            level2="设置",
            level3="字段",
            error_reason="测试",
            degree="轻微",
            reference="测试",
            detail_position="测试",
            prompted_fields1="test",
            type="测试",
            pos="测试",
            applicableArea="测试",
            default_use="是",
            start_date="2024-01-01",
            end_date="2024-12-31",
        )
        test_db_session.add(detail)
        test_db_session.commit()

        # Act
        detail.set_extended_field("new_field", "new_value")
        detail.set_extended_field("numeric_field", 42)

        # Assert
        assert detail.get_extended_field("new_field") == "new_value"
        assert detail.get_extended_field("numeric_field") == 42

    def test_get_all_extended_fields_should_return_all_fields(self, test_db_session, sample_template):
        """获取所有扩展字段应该返回所有字段"""
        # Arrange
        extended_data = {"field1": "value1", "field2": 123, "field3": True}
        detail = RuleDetail(
            rule_id="ALL_EXTENDED_TEST",
            rule_key=sample_template.rule_key,
            rule_name="获取所有扩展字段测试",
            level1="测试",
            level2="获取",
            level3="所有",
            error_reason="测试",
            degree="轻微",
            reference="测试",
            detail_position="测试",
            prompted_fields1="test",
            type="测试",
            pos="测试",
            applicableArea="测试",
            default_use="是",
            start_date="2024-01-01",
            end_date="2024-12-31",
            extended_fields=json.dumps(extended_data, ensure_ascii=False),
        )
        test_db_session.add(detail)
        test_db_session.commit()

        # Act
        result = detail.get_all_extended_fields()

        # Assert
        assert result == extended_data
        assert result["field1"] == "value1"
        assert result["field2"] == 123
        assert result["field3"] is True

    def test_update_extended_fields_should_merge_fields_correctly(self, test_db_session, sample_template):
        """批量更新扩展字段应该正确合并字段"""
        # Arrange
        initial_data = {"existing_field": "existing_value"}
        detail = RuleDetail(
            rule_id="UPDATE_EXTENDED_TEST",
            rule_key=sample_template.rule_key,
            rule_name="更新扩展字段测试",
            level1="测试",
            level2="更新",
            level3="字段",
            error_reason="测试",
            degree="轻微",
            reference="测试",
            detail_position="测试",
            prompted_fields1="test",
            type="测试",
            pos="测试",
            applicableArea="测试",
            default_use="是",
            start_date="2024-01-01",
            end_date="2024-12-31",
            extended_fields=json.dumps(initial_data, ensure_ascii=False),
        )
        test_db_session.add(detail)
        test_db_session.commit()

        # Act
        new_fields = {
            "new_field1": "new_value1",
            "new_field2": 456,
            "existing_field": "updated_value",  # 覆盖现有字段
        }
        detail.update_extended_fields(new_fields)

        # Assert
        all_fields = detail.get_all_extended_fields()
        assert all_fields["existing_field"] == "updated_value"  # 被覆盖
        assert all_fields["new_field1"] == "new_value1"
        assert all_fields["new_field2"] == 456

    def test_extended_fields_with_invalid_json_should_handle_gracefully(self, test_db_session, sample_template):
        """扩展字段包含无效JSON时应该优雅处理"""
        # Arrange
        detail = RuleDetail(
            rule_id="INVALID_JSON_TEST",
            rule_key=sample_template.rule_key,
            rule_name="无效JSON测试",
            level1="测试",
            level2="无效",
            level3="JSON",
            error_reason="测试",
            degree="轻微",
            reference="测试",
            detail_position="测试",
            prompted_fields1="test",
            type="测试",
            pos="测试",
            applicableArea="测试",
            default_use="是",
            start_date="2024-01-01",
            end_date="2024-12-31",
            extended_fields="invalid json string",  # 无效的JSON
        )
        test_db_session.add(detail)
        test_db_session.commit()

        # Act & Assert - 应该优雅处理，不抛出异常
        assert detail.get_extended_field("any_field") is None
        assert detail.get_extended_field("any_field", "default") == "default"
        assert detail.get_all_extended_fields() == {}

        # 设置新字段应该覆盖无效JSON
        detail.set_extended_field("new_field", "new_value")
        assert detail.get_extended_field("new_field") == "new_value"

    def test_relationship_with_template_should_work_correctly(self, test_db_session, sample_template):
        """与模板的关联关系应该正常工作"""
        # Arrange
        detail = RuleDetail(
            rule_id="RELATIONSHIP_TEST",
            rule_key=sample_template.rule_key,
            rule_name="关联关系测试",
            level1="测试",
            level2="关联",
            level3="关系",
            error_reason="测试",
            degree="轻微",
            reference="测试",
            detail_position="测试",
            prompted_fields1="test",
            type="测试",
            pos="测试",
            applicableArea="测试",
            default_use="是",
            start_date="2024-01-01",
            end_date="2024-12-31",
        )
        test_db_session.add(detail)
        test_db_session.commit()

        # Act & Assert
        assert detail.template is not None
        assert detail.template.rule_key == sample_template.rule_key
        assert detail.template.name == sample_template.name
