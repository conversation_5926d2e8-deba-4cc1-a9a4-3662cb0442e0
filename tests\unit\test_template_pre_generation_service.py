"""
Excel模板预生成服务测试
"""

import json
import tempfile
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from sqlalchemy.orm import Session

from models.database import FieldTypeEnum, RuleFieldMetadata, RuleTemplate, RuleTemplateStatusEnum
from services.template_pre_generation_service import (
    TemplateFileManager,
    TemplatePreGenerationService,
    TemplateVersionManager,
)


class TestTemplateVersionManager:
    """模板版本管理器测试"""

    def test_init_with_new_file(self):
        """测试初始化新版本文件"""
        with tempfile.TemporaryDirectory() as temp_dir:
            version_file = Path(temp_dir) / "test_versions.json"
            manager = TemplateVersionManager(str(version_file))

            assert manager.versions == {}
            assert version_file.parent.exists()

    def test_calculate_template_version(self):
        """测试计算模板版本"""
        with tempfile.TemporaryDirectory() as temp_dir:
            version_file = Path(temp_dir) / "test_versions.json"
            manager = TemplateVersionManager(str(version_file))

            # 创建模拟模板
            template = MagicMock(spec=RuleTemplate)
            template.rule_key = "test_rule"
            template.name = "测试规则"
            template.updated_at = None

            # 创建模拟字段元数据
            field_metadata = MagicMock(spec=RuleFieldMetadata)
            field_metadata.field_name = "test_field"
            field_metadata.field_type = FieldTypeEnum.string
            field_metadata.is_required = True
            field_metadata.display_name = "测试字段"
            field_metadata.excel_column_order = 1
            field_metadata.validation_rule = "required"

            template.get_field_metadata_list.return_value = [field_metadata]

            # 计算版本
            version = manager.calculate_template_version(template)

            assert isinstance(version, str)
            assert len(version) == 8  # MD5前8位

    def test_version_record_operations(self):
        """测试版本记录操作"""
        with tempfile.TemporaryDirectory() as temp_dir:
            version_file = Path(temp_dir) / "test_versions.json"
            manager = TemplateVersionManager(str(version_file))

            # 更新版本记录
            manager.update_version_record("test_rule", "abc12345")

            # 验证记录
            assert manager.get_current_version("test_rule") == "abc12345"
            assert version_file.exists()

            # 验证文件内容
            with open(version_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                assert data["test_rule"] == "abc12345"


class TestTemplateFileManager:
    """模板文件管理器测试"""

    def test_init_creates_directory(self):
        """测试初始化创建目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            base_dir = Path(temp_dir) / "templates"
            manager = TemplateFileManager(str(base_dir))

            assert base_dir.exists()
            assert base_dir.is_dir()

    def test_get_standard_path(self):
        """测试获取标准路径"""
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = TemplateFileManager(temp_dir)

            path = manager.get_standard_path("test_rule", "abc12345")
            expected = Path(temp_dir) / "test_rule_abc12345.xlsx"

            assert path == expected

    def test_get_template_file_with_version(self):
        """测试根据版本获取模板文件"""
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = TemplateFileManager(temp_dir)

            # 创建测试文件
            test_file = Path(temp_dir) / "test_rule_abc12345.xlsx"
            test_file.touch()

            # 测试存在的文件
            result = manager.get_template_file("test_rule", "abc12345")
            assert result == test_file

            # 测试不存在的文件
            result = manager.get_template_file("test_rule", "xyz67890")
            assert result is None

    def test_get_template_file_latest(self):
        """测试获取最新模板文件"""
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = TemplateFileManager(temp_dir)

            # 创建多个版本的文件
            file1 = Path(temp_dir) / "test_rule_abc12345.xlsx"
            file2 = Path(temp_dir) / "test_rule_def67890.xlsx"

            file1.touch()
            file2.touch()

            # 修改文件时间
            import time
            time.sleep(0.1)
            file2.touch()  # 更新修改时间

            # 获取最新文件
            result = manager.get_template_file("test_rule")
            assert result == file2

    def test_cleanup_old_versions(self):
        """测试清理旧版本文件"""
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = TemplateFileManager(temp_dir)

            # 创建多个版本的文件
            old_file = Path(temp_dir) / "test_rule_old123.xlsx"
            current_file = Path(temp_dir) / "test_rule_new456.xlsx"

            old_file.touch()
            current_file.touch()

            # 清理旧版本
            manager.cleanup_old_versions("test_rule", "new456")

            # 验证结果
            assert not old_file.exists()
            assert current_file.exists()


class TestTemplatePreGenerationService:
    """模板预生成服务测试"""

    @pytest.fixture
    def mock_session(self):
        """模拟数据库会话"""
        session = MagicMock(spec=Session)
        return session

    @pytest.fixture
    def mock_template(self):
        """模拟规则模板"""
        template = MagicMock(spec=RuleTemplate)
        template.rule_key = "test_rule"
        template.name = "测试规则"
        template.updated_at = None
        template.status = RuleTemplateStatusEnum.READY

        # 模拟字段元数据
        field_metadata = MagicMock(spec=RuleFieldMetadata)
        field_metadata.field_name = "test_field"
        field_metadata.field_type = FieldTypeEnum.string
        field_metadata.is_required = True
        field_metadata.display_name = "测试字段"
        field_metadata.excel_column_order = 1
        field_metadata.validation_rule = "required"

        template.get_field_metadata_list.return_value = [field_metadata]
        return template

    def test_init(self, mock_session):
        """测试服务初始化"""
        with tempfile.TemporaryDirectory() as temp_dir:
            service = TemplatePreGenerationService(mock_session, temp_dir)

            assert service.session == mock_session
            assert service.output_dir == temp_dir
            assert service.version_manager is not None
            assert service.file_manager is not None
            assert service.excel_service is not None

    @pytest.mark.asyncio
    async def test_generate_all_templates_on_startup(self, mock_session, mock_template):
        """测试启动时生成所有模板"""
        with tempfile.TemporaryDirectory() as temp_dir:
            service = TemplatePreGenerationService(mock_session, temp_dir)

            # 模拟数据库查询
            mock_session.query.return_value.all.return_value = [mock_template]

            # 模拟Excel服务
            with patch.object(service, '_generate_template_if_needed', new_callable=AsyncMock) as mock_generate:
                mock_generate.return_value = True

                result = await service.generate_all_templates_on_startup()

                assert result['total'] == 1
                assert result['generated'] == 1
                assert result['skipped'] == 0
                assert result['failed'] == 0
                assert 'duration' in result

    @pytest.mark.asyncio
    async def test_generate_template_if_needed_skip_existing(self, mock_session, mock_template):
        """测试跳过已存在的模板"""
        with tempfile.TemporaryDirectory() as temp_dir:
            service = TemplatePreGenerationService(mock_session, temp_dir)

            # 创建已存在的文件
            version = service.version_manager.calculate_template_version(mock_template)
            existing_file = service.file_manager.get_standard_path(mock_template.rule_key, version)
            existing_file.touch()

            result = await service._generate_template_if_needed(mock_template)

            assert result is False  # 应该跳过生成

    @pytest.mark.asyncio
    async def test_generate_template_if_needed_no_metadata(self, mock_session):
        """测试没有元数据的模板"""
        with tempfile.TemporaryDirectory() as temp_dir:
            service = TemplatePreGenerationService(mock_session, temp_dir)

            # 创建没有元数据的模板
            template = MagicMock(spec=RuleTemplate)
            template.rule_key = "test_rule"
            template.get_field_metadata_list.return_value = []

            result = await service._generate_template_if_needed(template)

            assert result is False  # 应该跳过生成

    def test_get_template_status(self, mock_session, mock_template):
        """测试获取模板状态"""
        with tempfile.TemporaryDirectory() as temp_dir:
            service = TemplatePreGenerationService(mock_session, temp_dir)

            # 模拟数据库查询
            mock_session.query.return_value.all.return_value = [mock_template]

            status_list = service.get_template_status()

            assert len(status_list) == 1
            status = status_list[0]
            assert status['rule_key'] == "test_rule"
            assert status['rule_name'] == "测试规则"
            assert 'current_version' in status
            assert 'file_exists' in status
