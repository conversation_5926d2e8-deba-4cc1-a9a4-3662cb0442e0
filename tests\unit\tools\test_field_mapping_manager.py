"""
字段映射管理器单元测试
测试FieldMappingManager工具类的核心功能，包括字段查询、转换、验证等
"""

import json
import pytest
import tempfile
from pathlib import Path
from unittest.mock import patch, mock_open

from tools.field_mapping_manager import FieldMappingManager, FieldMappingError


class TestFieldMappingManager:
    """字段映射管理器测试类
    
    测试范围：
    - 配置文件加载和验证
    - 字段查询功能
    - 字段转换功能
    - 验证规则获取
    - 规则类型管理
    - 错误处理
    """

    @pytest.fixture
    def sample_config(self):
        """示例配置数据"""
        return {
            "metadata": {
                "version": "3.1.0",
                "last_updated": "2024-01-01",
                "description": "测试配置"
            },
            "field_definitions": {
                "common_fields": {
                    "level1": {
                        "chinese_name": "一级错误类型",
                        "data_type": "string",
                        "database_column": "level1",
                        "api_field": "level1",
                        "excel_column": "一级错误类型",
                        "validation_rules": ["required", "max_length:255"]
                    },
                    "level2": {
                        "chinese_name": "二级错误类型",
                        "data_type": "string",
                        "database_column": "level2",
                        "api_field": "level2",
                        "excel_column": "二级错误类型",
                        "validation_rules": ["required", "max_length:255"]
                    }
                },
                "specific_fields": {
                    "custom_field": {
                        "chinese_name": "自定义字段",
                        "data_type": "string",
                        "database_column": "custom_field",
                        "api_field": "custom_field",
                        "excel_column": "自定义字段",
                        "validation_rules": ["max_length:100"]
                    }
                }
            },
            "rule_type_mappings": {
                "药品适应症": {
                    "required_fields": ["level1", "level2", "rule_name"],
                    "optional_fields": ["custom_field", "remarks"]
                },
                "数据质量": {
                    "required_fields": ["level1", "level2", "error_reason"],
                    "optional_fields": ["degree", "reference"]
                }
            }
        }

    @pytest.fixture
    def temp_config_file(self, sample_config):
        """创建临时配置文件"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
            json.dump(sample_config, f, ensure_ascii=False, indent=2)
            temp_path = f.name
        
        yield temp_path
        
        # 清理
        Path(temp_path).unlink(missing_ok=True)

    def test_init_with_valid_config_should_succeed(self, temp_config_file):
        """使用有效配置初始化应该成功"""
        # Act
        manager = FieldMappingManager(temp_config_file)

        # Assert
        assert manager.config is not None
        assert manager.config["metadata"]["version"] == "3.1.0"
        assert len(manager._field_cache) == 3  # level1, level2, custom_field
        assert len(manager._rule_type_cache) == 2  # 药品适应症, 数据质量

    def test_init_with_nonexistent_file_should_raise_error(self):
        """使用不存在的配置文件初始化应该抛出异常"""
        # Act & Assert
        with pytest.raises(FieldMappingError) as exc_info:
            FieldMappingManager("nonexistent_file.json")
        
        assert "字段映射配置文件不存在" in str(exc_info.value)

    def test_init_with_invalid_json_should_raise_error(self):
        """使用无效JSON配置文件初始化应该抛出异常"""
        # Arrange
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
            f.write("invalid json content")
            temp_path = f.name

        try:
            # Act & Assert
            with pytest.raises(FieldMappingError) as exc_info:
                FieldMappingManager(temp_path)
            
            assert "JSON格式错误" in str(exc_info.value)
        finally:
            Path(temp_path).unlink(missing_ok=True)

    def test_init_with_missing_required_sections_should_raise_error(self):
        """使用缺少必要节点的配置文件初始化应该抛出异常"""
        # Arrange
        invalid_config = {"metadata": {"version": "1.0.0"}}  # 缺少field_definitions和rule_type_mappings
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
            json.dump(invalid_config, f)
            temp_path = f.name

        try:
            # Act & Assert
            with pytest.raises(FieldMappingError) as exc_info:
                FieldMappingManager(temp_path)
            
            assert "缺少必要节点" in str(exc_info.value)
        finally:
            Path(temp_path).unlink(missing_ok=True)

    def test_get_field_definition_with_existing_field_should_return_definition(self, temp_config_file):
        """获取存在的字段定义应该返回正确的定义"""
        # Arrange
        manager = FieldMappingManager(temp_config_file)

        # Act
        result = manager.get_field_definition("level1")

        # Assert
        assert result is not None
        assert result["chinese_name"] == "一级错误类型"
        assert result["data_type"] == "string"
        assert result["database_column"] == "level1"
        assert result["validation_rules"] == ["required", "max_length:255"]

    def test_get_field_definition_with_nonexistent_field_should_return_none(self, temp_config_file):
        """获取不存在的字段定义应该返回None"""
        # Arrange
        manager = FieldMappingManager(temp_config_file)

        # Act
        result = manager.get_field_definition("nonexistent_field")

        # Assert
        assert result is None

    def test_get_chinese_name_should_return_correct_name(self, temp_config_file):
        """获取中文名称应该返回正确的名称"""
        # Arrange
        manager = FieldMappingManager(temp_config_file)

        # Act & Assert
        assert manager.get_chinese_name("level1") == "一级错误类型"
        assert manager.get_chinese_name("level2") == "二级错误类型"
        assert manager.get_chinese_name("custom_field") == "自定义字段"
        assert manager.get_chinese_name("nonexistent_field") == "nonexistent_field"  # 返回原字段名

    def test_get_standard_field_name_should_return_field_name(self, temp_config_file):
        """获取标准字段名应该返回字段名"""
        # Arrange
        manager = FieldMappingManager(temp_config_file)

        # Act & Assert
        assert manager.get_standard_field_name("level1") == "level1"
        assert manager.get_standard_field_name("level2") == "level2"
        assert manager.get_standard_field_name("nonexistent_field") == "nonexistent_field"

    def test_get_database_column_should_return_correct_column(self, temp_config_file):
        """获取数据库列名应该返回正确的列名"""
        # Arrange
        manager = FieldMappingManager(temp_config_file)

        # Act & Assert
        assert manager.get_database_column("level1") == "level1"
        assert manager.get_database_column("level2") == "level2"
        assert manager.get_database_column("custom_field") == "custom_field"
        assert manager.get_database_column("nonexistent_field") == "nonexistent_field"

    def test_get_api_field_should_return_correct_field(self, temp_config_file):
        """获取API字段名应该返回正确的字段名"""
        # Arrange
        manager = FieldMappingManager(temp_config_file)

        # Act & Assert
        assert manager.get_api_field("level1") == "level1"
        assert manager.get_api_field("level2") == "level2"
        assert manager.get_api_field("custom_field") == "custom_field"
        assert manager.get_api_field("nonexistent_field") == "nonexistent_field"

    def test_get_excel_column_should_return_correct_column(self, temp_config_file):
        """获取Excel列名应该返回正确的列名"""
        # Arrange
        manager = FieldMappingManager(temp_config_file)

        # Act & Assert
        assert manager.get_excel_column("level1") == "一级错误类型"
        assert manager.get_excel_column("level2") == "二级错误类型"
        assert manager.get_excel_column("custom_field") == "自定义字段"
        assert manager.get_excel_column("nonexistent_field") == "nonexistent_field"

    def test_get_validation_rules_should_return_correct_rules(self, temp_config_file):
        """获取验证规则应该返回正确的规则"""
        # Arrange
        manager = FieldMappingManager(temp_config_file)

        # Act & Assert
        assert manager.get_validation_rules("level1") == ["required", "max_length:255"]
        assert manager.get_validation_rules("level2") == ["required", "max_length:255"]
        assert manager.get_validation_rules("custom_field") == ["max_length:100"]
        assert manager.get_validation_rules("nonexistent_field") == []

    def test_get_rule_type_fields_should_return_correct_fields(self, temp_config_file):
        """获取规则类型字段应该返回正确的字段"""
        # Arrange
        manager = FieldMappingManager(temp_config_file)

        # Act
        result1 = manager.get_rule_type_fields("药品适应症")
        result2 = manager.get_rule_type_fields("数据质量")
        result3 = manager.get_rule_type_fields("不存在的规则类型")

        # Assert
        assert result1["required"] == ["level1", "level2", "rule_name"]
        assert result1["optional"] == ["custom_field", "remarks"]
        
        assert result2["required"] == ["level1", "level2", "error_reason"]
        assert result2["optional"] == ["degree", "reference"]
        
        assert result3["required"] == []
        assert result3["optional"] == []

    def test_get_all_field_names_should_return_all_fields(self, temp_config_file):
        """获取所有字段名称应该返回所有字段"""
        # Arrange
        manager = FieldMappingManager(temp_config_file)

        # Act
        result = manager.get_all_field_names()

        # Assert
        assert len(result) == 3
        assert "level1" in result
        assert "level2" in result
        assert "custom_field" in result

    def test_get_common_fields_should_return_common_fields(self, temp_config_file):
        """获取通用字段应该返回通用字段"""
        # Arrange
        manager = FieldMappingManager(temp_config_file)

        # Act
        result = manager.get_common_fields()

        # Assert
        assert len(result) == 2
        assert "level1" in result
        assert "level2" in result
        assert "custom_field" not in result

    def test_get_specific_fields_should_return_specific_fields(self, temp_config_file):
        """获取特定字段应该返回特定字段"""
        # Arrange
        manager = FieldMappingManager(temp_config_file)

        # Act
        result = manager.get_specific_fields()

        # Assert
        assert len(result) == 1
        assert "custom_field" in result
        assert "level1" not in result

    def test_reload_config_should_reload_successfully(self, temp_config_file, sample_config):
        """重新加载配置应该成功"""
        # Arrange
        manager = FieldMappingManager(temp_config_file)
        original_version = manager.config["metadata"]["version"]

        # 修改配置文件
        sample_config["metadata"]["version"] = "3.2.0"
        with open(temp_config_file, "w", encoding="utf-8") as f:
            json.dump(sample_config, f, ensure_ascii=False, indent=2)

        # Act
        manager.reload_config()

        # Assert
        assert manager.config["metadata"]["version"] == "3.2.0"
        assert manager.config["metadata"]["version"] != original_version

    def test_get_config_info_should_return_correct_info(self, temp_config_file):
        """获取配置信息应该返回正确的信息"""
        # Arrange
        manager = FieldMappingManager(temp_config_file)

        # Act
        result = manager.get_config_info()

        # Assert
        assert result["config_loaded"] is True
        assert result["version"] == "3.1.0"
        assert result["description"] == "测试配置"
        assert result["total_fields"] == 3
        assert result["common_fields_count"] == 2
        assert result["specific_fields_count"] == 1
        assert result["rule_types_count"] == 2
        assert "field_definitions" in result
