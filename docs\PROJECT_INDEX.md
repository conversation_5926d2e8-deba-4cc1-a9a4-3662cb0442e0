# 项目文档索引 - 患者数据规则校验服务

> **最后更新**: 2025-08-02
> **状态**: Active
> **文档版本**: v2.2

## 🏗️ 项目概览

患者数据规则校验服务是一个基于 FastAPI 的高性能分布式系统，采用主从架构设计，支持医疗数据规则验证、规则管理和离线部署。

### 核心特性
- **主从架构**: 支持Master节点规则管理和Slave节点高性能验证
- **双模式同步**: 支持在线实时同步和离线包部署两种模式
- **并行处理**: 基于multiprocessing.Pool的多进程并行验证
- **智能缓存**: 多级缓存系统，支持版本缓存、变更缓存和包缓存
- **离线部署**: 完全离线运行，支持离线包导入和完整性校验
- **环境适配**: 开发、测试、生产环境特定配置

### 技术栈
- **后端**: FastAPI + SQLAlchemy + MySQL
- **前端**: Vue 3 + TypeScript + Element Plus
- **缓存**: Redis + 内存缓存
- **部署**: Docker + Docker Compose
- **监控**: 自研监控系统 + 日志系统

---

## 📚 核心文档目录

### 🚀 快速开始
| 文档 | 描述 | 路径 |
|------|------|------|
| **项目README** | 项目主要介绍和安装指南 | [README.md](../README.md) |
| **安装部署指南** | 完整的部署和配置指南 | [docs/user-guide/安装部署指南.md](user-guide/安装部署指南.md) |
| **双模式同步快速开始** | 5分钟快速体验双模式同步 | [docs/user-guide/dual_mode_sync_quick_start.md](user-guide/dual_mode_sync_quick_start.md) |
| **配置说明** | 环境变量和配置文件说明 | [docs/user-guide/配置说明.md](user-guide/配置说明.md) |
| **同步配置指南** | 双模式同步配置详解 | [docs/configuration/sync_configuration_guide.md](configuration/sync_configuration_guide.md) |

### 🏗️ 系统架构
| 文档 | 描述 | 路径 |
|------|------|------|
| **系统架构概览** | 整体架构设计和组件关系 | [docs/development/architecture/系统架构概览.md](development/architecture/系统架构概览.md) |
| **主从架构设计** | Master-Slave架构详细设计 | [docs/development/architecture/主从架构设计.md](development/architecture/主从架构设计.md) |
| **规则服务优化架构** | 性能优化和多进程处理 | [docs/development/architecture/rule_service_optimization_arch.md](development/architecture/rule_service_optimization_arch.md) |
| **内存泄漏缓解架构** | 内存管理和优化策略 | [docs/development/architecture/memory_leak_mitigation_arch.md](development/architecture/memory_leak_mitigation_arch.md) |

### 📖 API文档
| 文档 | 描述 | 路径 |
|------|------|------|
| **API快速参考** | 主要API接口快速查看 | [docs/development/api/API快速参考.md](development/api/API快速参考.md) |
| **规则查询API** | 规则查询接口详细说明 | [docs/development/api/rule_query_api.md](development/api/rule_query_api.md) |
| **双模式同步API** | 在线同步和离线包管理API | [docs/api/dual_mode_sync_api_reference.md](api/dual_mode_sync_api_reference.md) |
| **降级API参考** | 服务降级机制API | [docs/development/api/degradation-api-reference.md](development/api/degradation-api-reference.md) |
| **降级配置指南** | 降级策略配置方法 | [docs/development/api/degradation-configuration-guide.md](development/api/degradation-configuration-guide.md) |

### 🛠️ 开发指南
| 分类 | 文档 | 描述 | 路径 |
|------|------|------|------|
| **后端** | 智能批处理指南 | 批处理优化配置 | [docs/development/backend/intelligent_batch_processing_guide.md](development/backend/intelligent_batch_processing_guide.md) |
| **后端** | HTTP重试配置指南 | 网络重试机制配置 | [docs/development/backend/HTTP_RETRY_CONFIG_GUIDE.md](development/backend/HTTP_RETRY_CONFIG_GUIDE.md) |
| **后端** | 智能熔断器指南 | 熔断器模式实现 | [docs/development/backend/SMART_CIRCUIT_BREAKER_GUIDE.md](development/backend/SMART_CIRCUIT_BREAKER_GUIDE.md) |
| **前端** | 组件使用指南 | Vue组件开发规范 | [docs/development/frontend/组件使用指南.md](development/frontend/组件使用指南.md) |
| **前端** | 设计系统 | UI设计规范和组件库 | [docs/development/frontend/设计系统.md](development/frontend/设计系统.md) |
| **前端** | 状态管理 | Vuex/Pinia状态管理 | [docs/development/frontend/状态管理.md](development/frontend/状态管理.md) |

### 🗄️ 数据库相关
| 文档 | 描述 | 路径 |
|------|------|------|
| **数据库重构设计** | 规则详情表重构方案 | [docs/project/design/数据库重构设计.md](project/design/数据库重构设计.md) |
| **数据库字段标准化** | 字段命名和类型规范 | [docs/development/database/数据库字段标准化文档.md](development/database/数据库字段标准化文档.md) |
| **规则数据迁移手册** | 数据迁移操作指南 | [docs/operations/maintenance/规则数据迁移手册.md](operations/maintenance/规则数据迁移手册.md) |

### 🧪 测试文档
| 文档 | 描述 | 路径 |
|------|------|------|
| **API测试报告** | 接口测试结果报告 | [docs/development/testing/API测试报告.md](development/testing/API测试报告.md) |
| **前端测试检查清单** | 前端测试规范清单 | [docs/development/testing/前端测试检查清单.md](development/testing/前端测试检查清单.md) |
| **数据库模型测试指南** | 数据模型单元测试 | [docs/development/testing/数据库模型测试指南.md](development/testing/数据库模型测试指南.md) |

### 🚀 部署运维
| 文档 | 描述 | 路径 |
|------|------|------|
| **生产部署指南** | 生产环境部署流程 | [docs/operations/deployment/生产部署指南.md](operations/deployment/生产部署指南.md) |
| **Docker部署指南** | 容器化部署方案 | [docs/operations/deployment/Docker部署指南.md](operations/deployment/Docker部署指南.md) |
| **双模式同步部署指南** | 在线同步和离线包部署 | [docs/operations/deployment/dual_mode_sync_deployment_guide.md](operations/deployment/dual_mode_sync_deployment_guide.md) |
| **离线部署指南** | 离线环境部署方法 | [docs/operations/deployment/离线部署指南.md](operations/deployment/离线部署指南.md) |
| **系统运维手册** | 日常运维操作指南 | [docs/operations/maintenance/系统运维手册.md](operations/maintenance/系统运维手册.md) |
| **双模式同步运维手册** | 双模式同步运维指导 | [docs/operations/maintenance/dual_mode_sync_maintenance_guide.md](operations/maintenance/dual_mode_sync_maintenance_guide.md) |
| **数据库配置指南** | 数据库优化配置 | [docs/operations/maintenance/database_configuration_guide.md](operations/maintenance/database_configuration_guide.md) |

### 📊 监控日志
| 文档 | 描述 | 路径 |
|------|------|------|
| **日志系统配置** | 环境特定日志配置 | [docs/operations/monitoring/logging_system.md](operations/monitoring/logging_system.md) |
| **日志配置详细指南** | 日志系统详细配置 | [docs/operations/monitoring/LOGGING_CONFIGURATION.md](operations/monitoring/LOGGING_CONFIGURATION.md) |

### 📋 项目报告
| 文档 | 描述 | 路径 |
|------|------|------|
| **规则注册开发总结** | 规则注册功能开发记录 | [docs/project/reports/rule_registration_development_summary.md](project/reports/rule_registration_development_summary.md) |
| **前端最终解决方案总结** | 前端问题解决方案汇总 | [docs/project/reports/前端最终解决方案总结.md](project/reports/前端最终解决方案总结.md) |
| **前端错误修复报告** | 前端bug修复记录 | [docs/project/reports/前端错误修复报告.md](project/reports/前端错误修复报告.md) |

---

## 🗂️ 目录结构说明

```
docs/
├── archive/                    # 历史文档存档
│   └── deprecated/            # 已废弃的文档
├── development/               # 开发相关文档
│   ├── api/                  # API接口文档
│   ├── architecture/         # 系统架构设计
│   ├── backend/              # 后端开发指南
│   ├── frontend/             # 前端开发指南
│   ├── database/             # 数据库相关
│   └── testing/              # 测试文档
├── operations/               # 运维部署文档
│   ├── deployment/           # 部署指南
│   ├── maintenance/          # 运维手册
│   └── monitoring/           # 监控日志
├── project/                  # 项目管理文档
│   ├── design/               # 设计文档
│   ├── reports/              # 项目报告
│   └── requirements/         # 需求文档
└── user-guide/               # 用户使用指南
```

---

## 🔧 开发环境设置

### 必需工具
- Python 3.9+
- Node.js 16+
- MySQL 8.0+
- Redis 6.0+
- Docker & Docker Compose

### 快速启动
```bash
# 1. 克隆项目
git clone <repository-url>
cd rule_slave_tmp

# 2. 配置环境
cp .env.example .env
# 编辑 .env 文件配置数据库连接

# 3. 启动服务
# Master节点
python master.py

# Slave节点
python slave.py

# 前端开发服务器
cd frontend
npm install
npm run dev
```

---

## 📞 支持与联系

### 开发团队
- **项目负责人**: [待填写]
- **架构师**: [待填写]
- **前端负责人**: [待填写]
- **运维负责人**: [待填写]

### 问题反馈
- **Bug报告**: 使用项目Issue系统
- **功能建议**: 联系项目负责人
- **文档问题**: 提交文档改进PR

---

## 📝 文档维护

### 更新记录
| 版本 | 日期 | 更新内容 | 更新人 |
|------|------|----------|--------|
| v2.1 | 2025-07-25 | 创建项目文档索引 | Claude |
| v2.0 | 2025-07-23 | 重构文档结构 | 开发团队 |

### 维护规范
1. **定期更新**: 每月检查文档时效性
2. **版本控制**: 重要更新需更新版本号
3. **链接检查**: 确保所有文档链接有效
4. **格式规范**: 遵循Markdown格式标准

---

## 🔍 快速查找

### 常见问题速查
- **部署问题**: 查看 [部署指南](operations/deployment/)
- **API使用**: 查看 [API文档](development/api/)
- **配置问题**: 查看 [配置说明](user-guide/配置说明.md)
- **性能优化**: 查看 [架构文档](development/architecture/)
- **故障排查**: 查看 [运维手册](operations/maintenance/)

### 按角色查看文档
- **开发者**: [开发指南](development/) + [API文档](development/api/)
- **运维人员**: [部署指南](operations/deployment/) + [运维手册](operations/maintenance/)
- **项目经理**: [项目报告](project/reports/) + [需求文档](project/requirements/)
- **测试人员**: [测试文档](development/testing/)

---

*本文档将持续更新，确保为团队提供最新、最准确的项目信息。*