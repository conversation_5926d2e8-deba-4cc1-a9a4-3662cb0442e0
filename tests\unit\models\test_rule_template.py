"""
规则模板模型单元测试
测试RuleTemplate模型的核心功能，包括创建、序列化、验证和关联关系
"""

import pytest
from datetime import datetime
from sqlalchemy.exc import IntegrityError

from models.database import RuleTemplate, RuleTemplateStatusEnum, RuleFieldMetadata, FieldTypeEnum


class TestRuleTemplate:
    """规则模板模型测试类
    
    测试范围：
    - 模型创建和基本属性
    - 序列化和反序列化
    - 数据验证
    - 关联关系
    - 约束检查
    """

    def test_create_rule_template_with_valid_data_should_succeed(self, test_db_session):
        """使用有效数据创建规则模板应该成功"""
        # Arrange
        template_data = {
            "rule_key": "test_rule_001",
            "rule_type": "药品适应症",
            "name": "测试规则模板001",
            "description": "这是一个测试规则模板",
            "module_path": "rules.test.test_rule_001",
            "file_hash": "abc123def456",
            "status": RuleTemplateStatusEnum.READY
        }

        # Act
        template = RuleTemplate(**template_data)
        test_db_session.add(template)
        test_db_session.commit()

        # Assert
        assert template.id is not None
        assert template.rule_key == "test_rule_001"
        assert template.rule_type == "药品适应症"
        assert template.name == "测试规则模板001"
        assert template.description == "这是一个测试规则模板"
        assert template.module_path == "rules.test.test_rule_001"
        assert template.file_hash == "abc123def456"
        assert template.status == RuleTemplateStatusEnum.READY
        assert template.created_at is not None
        assert template.updated_at is not None

    def test_create_rule_template_with_minimal_data_should_succeed(self, test_db_session):
        """使用最小必填数据创建规则模板应该成功"""
        # Arrange
        template_data = {
            "rule_key": "minimal_rule",
            "rule_type": "基础规则",
            "name": "最小规则模板"
        }

        # Act
        template = RuleTemplate(**template_data)
        test_db_session.add(template)
        test_db_session.commit()

        # Assert
        assert template.id is not None
        assert template.rule_key == "minimal_rule"
        assert template.rule_type == "基础规则"
        assert template.name == "最小规则模板"
        assert template.description is None
        assert template.module_path is None
        assert template.file_hash is None
        assert template.status == RuleTemplateStatusEnum.NEW  # 默认状态

    def test_create_rule_template_with_duplicate_rule_key_should_fail(self, test_db_session):
        """创建重复rule_key的规则模板应该失败"""
        # Arrange
        template1 = RuleTemplate(
            rule_key="duplicate_key",
            rule_type="规则类型1",
            name="规则模板1"
        )
        template2 = RuleTemplate(
            rule_key="duplicate_key",  # 重复的rule_key
            rule_type="规则类型2",
            name="规则模板2"
        )

        # Act & Assert
        test_db_session.add(template1)
        test_db_session.commit()

        test_db_session.add(template2)
        with pytest.raises(IntegrityError):
            test_db_session.commit()

    def test_to_dict_should_return_correct_format(self, test_db_session):
        """to_dict方法应该返回正确的字典格式"""
        # Arrange
        template = RuleTemplate(
            rule_key="dict_test",
            rule_type="字典测试",
            name="字典测试模板",
            description="测试to_dict方法",
            status=RuleTemplateStatusEnum.READY
        )
        test_db_session.add(template)
        test_db_session.commit()

        # Act
        result = template.to_dict()

        # Assert
        assert isinstance(result, dict)
        assert result["rule_key"] == "dict_test"
        assert result["rule_type"] == "字典测试"
        assert result["name"] == "字典测试模板"
        assert result["description"] == "测试to_dict方法"
        assert result["status"] == "READY"
        assert "id" in result
        assert "created_at" in result
        assert "updated_at" in result

    def test_from_dict_should_create_valid_instance(self):
        """from_dict方法应该创建有效的实例"""
        # Arrange
        data = {
            "rule_key": "from_dict_test",
            "rule_type": "从字典创建",
            "name": "从字典创建的模板",
            "description": "测试from_dict方法",
            "module_path": "test.module",
            "file_hash": "hash123",
            "status": "READY"
        }

        # Act
        template = RuleTemplate.from_dict(data)

        # Assert
        assert template.rule_key == "from_dict_test"
        assert template.rule_type == "从字典创建"
        assert template.name == "从字典创建的模板"
        assert template.description == "测试from_dict方法"
        assert template.module_path == "test.module"
        assert template.file_hash == "hash123"
        assert template.status == RuleTemplateStatusEnum.READY

    def test_from_dict_should_exclude_system_fields(self):
        """from_dict方法应该排除系统字段"""
        # Arrange
        data = {
            "id": 999,  # 应该被排除
            "rule_key": "exclude_test",
            "rule_type": "排除测试",
            "name": "排除系统字段测试",
            "created_at": "2023-01-01T00:00:00",  # 应该被排除
            "updated_at": "2023-01-01T00:00:00"   # 应该被排除
        }

        # Act
        template = RuleTemplate.from_dict(data)

        # Assert
        assert template.id is None  # 系统字段被排除
        assert template.rule_key == "exclude_test"
        assert template.rule_type == "排除测试"
        assert template.name == "排除系统字段测试"

    def test_validate_template_with_valid_data_should_pass(self, test_db_session):
        """验证有效模板数据应该通过"""
        # Arrange
        template = RuleTemplate(
            rule_key="valid_template",
            rule_type="有效模板",
            name="有效的模板"
        )
        test_db_session.add(template)
        test_db_session.commit()

        # Act
        result = template.validate_template()

        # Assert
        assert result["valid"] is True
        assert len(result["errors"]) == 0

    def test_validate_template_with_missing_required_fields_should_fail(self):
        """验证缺少必填字段的模板应该失败"""
        # Arrange
        template = RuleTemplate()  # 缺少所有必填字段

        # Act
        result = template.validate_template()

        # Assert
        assert result["valid"] is False
        assert "rule_key不能为空" in result["errors"]
        assert "name不能为空" in result["errors"]
        assert "rule_type不能为空" in result["errors"]

    def test_get_field_metadata_list_should_return_associated_metadata(self, test_db_session):
        """获取字段元数据列表应该返回关联的元数据"""
        # Arrange
        template = RuleTemplate(
            rule_key="metadata_test",
            rule_type="元数据测试",
            name="元数据测试模板"
        )
        test_db_session.add(template)
        test_db_session.flush()  # 获取ID但不提交

        # 添加字段元数据
        metadata1 = RuleFieldMetadata(
            rule_key="metadata_test",
            field_name="field1",
            field_type=FieldTypeEnum.string,
            display_name="字段1"
        )
        metadata2 = RuleFieldMetadata(
            rule_key="metadata_test",
            field_name="field2",
            field_type=FieldTypeEnum.integer,
            display_name="字段2"
        )
        test_db_session.add_all([metadata1, metadata2])
        test_db_session.commit()

        # Act
        metadata_list = template.get_field_metadata_list()

        # Assert
        assert len(metadata_list) == 2
        field_names = [m.field_name for m in metadata_list]
        assert "field1" in field_names
        assert "field2" in field_names

    def test_get_field_metadata_by_name_should_return_correct_metadata(self, test_db_session):
        """根据字段名获取元数据应该返回正确的元数据"""
        # Arrange
        template = RuleTemplate(
            rule_key="get_metadata_test",
            rule_type="获取元数据测试",
            name="获取元数据测试模板"
        )
        test_db_session.add(template)
        test_db_session.flush()

        metadata = RuleFieldMetadata(
            rule_key="get_metadata_test",
            field_name="target_field",
            field_type=FieldTypeEnum.string,
            display_name="目标字段"
        )
        test_db_session.add(metadata)
        test_db_session.commit()

        # Act
        result = template.get_field_metadata_by_name("target_field")

        # Assert
        assert result is not None
        assert result.field_name == "target_field"
        assert result.display_name == "目标字段"

    def test_get_field_metadata_by_name_with_nonexistent_field_should_return_none(self, test_db_session):
        """获取不存在的字段元数据应该返回None"""
        # Arrange
        template = RuleTemplate(
            rule_key="nonexistent_test",
            rule_type="不存在测试",
            name="不存在字段测试模板"
        )
        test_db_session.add(template)
        test_db_session.commit()

        # Act
        result = template.get_field_metadata_by_name("nonexistent_field")

        # Assert
        assert result is None

    def test_cascade_delete_should_remove_associated_records(self, test_db_session):
        """级联删除应该移除关联记录"""
        # Arrange
        template = RuleTemplate(
            rule_key="cascade_test",
            rule_type="级联测试",
            name="级联删除测试模板"
        )
        test_db_session.add(template)
        test_db_session.flush()

        # 添加关联的字段元数据
        metadata = RuleFieldMetadata(
            rule_key="cascade_test",
            field_name="test_field",
            field_type=FieldTypeEnum.string,
            display_name="测试字段"
        )
        test_db_session.add(metadata)
        test_db_session.commit()

        # 验证记录存在
        assert test_db_session.query(RuleFieldMetadata).filter_by(rule_key="cascade_test").count() == 1

        # Act - 删除模板
        test_db_session.delete(template)
        test_db_session.commit()

        # Assert - 关联记录也应该被删除
        assert test_db_session.query(RuleFieldMetadata).filter_by(rule_key="cascade_test").count() == 0
